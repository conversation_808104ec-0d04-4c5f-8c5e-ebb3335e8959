### 50系初见

### Q1

#### 关于50系的若干讨论

问题一：为什么功耗爆炸规格堆到爆炸？
答：因为能效比和芯片制程（单位面积晶体管密度)强相关，本次和40系属于同一世代的4nm工艺，故理论上不存在本质的能效比提升，只有架构优化好和堆料。台积电的3nm工艺不是不开放，而是优先供给给NVIDIA的AI加速卡和苹果M系列，两个金主都是因为销售高附加值产品利润率高所以财大气粗，从产品角度来说前者有TOC（total owner cost）的需求继续降低功耗，后者也有移动端持续发力能效的目标。唯独消费级的pc独立显卡，无论是卖给游戏本的阉割core和独立的显砖，都因为过往x86阵营留下的刻板印象，此时能效显得不那么重要。退一万步讲，强行使用3nm工艺，老黄购买核心的价格买不起，消费级产品线的玩家购买力面对高昂显卡价格买不起，台积电流片成功率和交付产能拖不起，综上，50系就是堆料的一代。

问题2:为什么拼好帧？
DLSS涉及两个核心功能概念，分辨率上采样和帧生成。前者是基于CNN和大量训练实现的，有模糊、画质变化、细节错误等问题，后者是基于联系前一帧后一阵然后插值计算实现的，虽然能够访问程序内部矢量信息，但是对于屏幕边缘或者快速运动变化的插帧效果仍旧有限，有鬼影拖影闪光等问题。
此次模型转向，明显是因为看到了生成式大模型利用transformer的自注意力机制实现了远超cnn的效果，能够做到更合理的上采样（对训练素材更有效的学习），也能做到更快速精确的插帧生成。之前无法设计更高倍率的插帧，明显是因为算力和帧生成时间-密集问题，而transformer的引入实现了更低的显存占用和更快地插帧生成，所以本次有了x2到x4的质的飞跃。
本身光栅化性能平均30%提高已经足够，但是老黄感觉没有新意没有噱头，为了符合其all in AI的公司战略，所以引入这一增值功能。进一步拉开AMD差距。

问题3:为什么锁AI tops？会不会影响游戏性能？
初步目测硬件没有锁，是软件锁。锁的是RT core算力，这部分既是dlss专门计算分化出来的硬件核心，也是大模型推理的加速计算核心。目测会对大模型推理速度构成影响，但是dlss是否有机会摸到RT core被锁算力的瓶颈有待验证，普遍观点认为一般游戏性能上是不会受到明显约束。换句话说，本身在迈过某个RT core数量节点之后，dlss对RT core的占用根本达不到100%，所以RT core从设计上讲就有闲置。

问题4:为什么纯光栅性能测不出区别？
cpu瓶颈只是最容易想到的瓶颈，本次5090显然确实有光栅提升和显存位宽吞吐量的提升，所以瓶颈必然不在显卡。内存瓶颈、总线瓶颈也是潜在因素。但是更大的可能是本身游戏优化的瓶颈，只有内部完全是由系统层次编程语言写成机器码且不存在排序队列问题的游戏才有可能实现无上限的帧数提高。在测试中很多5090和前代无提高的光栅数值悖论，很明显是游戏优化的问题——任何死锁、等待、排序都会造成cpu空转，也就是占用无法提高而cpu帧上限被锁死，这和cpu瓶颈无关，严格意义上就是优化问题导致cpu等待。知名优化游戏cs每代都能在纯光栅性能中获得最高的百分比提升，说明其游戏逻辑内部针对高刷新率本身就有了充足的基石设计，从容规避任何cpu空转（而非瓶颈）等闲置问题。这就是关于gpu占用不满，cpu占用不满，而很多网友乱评论cpu瓶颈的原因，因为他们脑子里有一种错误的常识——谁占用满谁就瓶颈了，显卡没跑满，那一定是cpu的锅。实际上9800x3d绰绰有余，同时代架构换成9950x/9950x3d针对这些”优化悖论游戏“也不会有巨大提升。

问题5:谁最值得买?
首推90系列，因为位宽被解禁，显存大增强，即使溢价也值得。
80系被批评最惨，位宽、显存毫无建树，规格差距拉大，作为ai放大的跳板机。

问题6:超高帧生成有什么问题？
小黄鸭（帧生成软件）的存在说明一个现实，60帧的信息量基本足够描绘世界真实性，信息损失（运动层面）极低，此时插帧因为基础帧数足够高，帧间差异小，所以插帧很丝滑，多出的帧率实现了对人眼的运动补偿，感到舒适。
AMD的FSR算法惨遭诟病的一大原因是，本身A家显卡光栅就弱，开源的其他低端gpu更是本身帧数就不足，谁有足够的基础帧数信息来跑FSR？大多数都是帧数低于60强行开启，此时效果最差。
N卡因为dlss走独立的RTcore算力，不与光栅算力竞争（小黄鸭、fsr就走光栅算力），所以不存在资源竞争问题，其次本身dlss一定对不同挡位有动态变化的设计，尽可能保证插帧前低分辨率接近40-50,大幅提高最终插帧后稳定性。









