# 纯问题集问答重建系统 - Prompt设计说明

## 设计概述

我为您设计了一个完整的prompt系统，用于引导后续大模型处理纯问题集并生成高质量的问答对话。

## 📁 Prompt文件结构

### 核心文件
1. **`问答重建系统Prompt.md`** - 完整详细的执行指南
2. **`快速执行指南.md`** - 简化版执行指令
3. **`回答格式示例.md`** - 期望输出格式的具体示例
4. **`Prompt设计说明.md`** - 本说明文档

### 数据文件
- **30个问题文件** - 包含Q1-Qn格式的纯问题
- **`README.md`** - 问题集说明文档

## 🎯 Prompt设计要点

### 1. 文件结构关系说明
- **输入结构**: 清晰描述30个问题文件的组织方式
- **输出结构**: 详细说明目标文件夹和命名规则
- **一一对应**: 确保每个问题文件都有对应的回答文件

### 2. Q与Q之间的标注区分
- **问题识别**: 使用`## Q1`, `## Q2`等标准格式
- **上下文关系**: 明确Q2基于Q1，Q3基于Q1-Q2的累积上下文
- **类型分类**: 区分YouTube转写、深度分析、对话整理等不同类型

### 3. 目标生成格式
- **标准模板**: 提供`### Q1` + `### A1`的对话格式
- **质量要求**: 每个回答至少200字，保持专业性和实用性
- **连贯性**: 确保文件内对话的自然流畅

### 4. 工作流集成
- **任务管理**: 使用`add_tasks`为每个文件创建独立任务
- **进度跟踪**: 建立详细的进度报告机制
- **状态更新**: 及时更新任务状态（IN_PROGRESS → COMPLETE）

## 🔧 技术实现特点

### 文件处理策略
```
读取问题文件 → 解析Q1-Qn → 生成A1-An → 保存回答文件 → 更新进度
```

### 上下文处理机制
```
Q1 → A1 (建立基础)
Q2 → A2 (基于Q1-A1)
Q3 → A3 (基于Q1-A1, Q2-A2)
...
```

### 质量控制体系
- **准确性**: 基于真实信息和专业知识
- **完整性**: 充分回答问题的所有方面
- **实用性**: 提供可操作的建议和信息
- **专业性**: 使用适当的专业术语和深度

## 📋 问题类型处理策略

### YouTube视频转写类 (60%)
- 技术实现方案 (Whisper, API等)
- 工具推荐和使用方法
- 质量控制和验证步骤
- 格式化和后处理技巧

### 深度分析类 (25%)
- 多维度分析框架
- 理论基础和实例支撑
- 数据和事实引用
- 不同观点的平衡呈现

### 对话历史整理类 (10%)
- 标准的Markdown格式化方法
- 自动化工具建议
- 质量检查方法

### 专业咨询类 (5%)
- 具体操作步骤
- 工具和资源推荐
- 注意事项和风险提示

## 🎯 预期成果

### 数量指标
- **30个回答文件**: 对应30个问题文件
- **220+个回答**: 覆盖所有Q编号
- **完整进度报告**: 详细的处理记录

### 质量指标
- **专业性**: 每个回答都具备专业深度
- **实用性**: 提供可操作的价值
- **连贯性**: 保持上下文的自然流畅
- **完整性**: 全面回答问题各个方面

## 🚀 使用建议

### 对后续大模型的建议
1. **按顺序执行**: 严格按照prompt中的步骤执行
2. **分批处理**: 建议每5个文件进行一次质量检查
3. **保持专业**: 确保回答的准确性和专业性
4. **记录进度**: 及时更新任务状态和进度报告

### 质量检查要点
- [ ] 每个Q都有对应的A
- [ ] 回答内容专业准确
- [ ] 格式符合规范
- [ ] 上下文连贯自然
- [ ] 进度报告完整

## 📝 总结

这个prompt系统设计完整、结构清晰，能够有效引导后续大模型：

1. **理解文件结构**: 清楚输入和输出的组织方式
2. **识别问题类型**: 针对不同类型采用相应策略
3. **生成高质量回答**: 保持专业性和实用性
4. **维护工作流**: 集成任务管理和进度跟踪
5. **确保质量**: 建立完整的质量控制体系

通过这个系统，后续大模型可以高效地将220+个纯问题转换为高质量的问答对话数据集。

---

**设计完成时间**: 2025-08-19 01:25  
**设计者**: Augment Agent  
**适用场景**: 大规模问答数据集重建任务
