# 纯问题集问答重建系统 - 执行Prompt

## 任务概述

您需要基于现有的纯问题集文件夹，重新生成高质量的问答对话，建立完整的问答数据集。

## 文件结构说明

### 输入结构
```
工作目录/纯问题集重建/
├── README.md                    # 问题集说明文档
├── 11维度.md                    # 问题文件1 (包含Q1-Q6)
├── Apple外区id指南.md            # 问题文件2 (包含Q1)
├── telegram创始人发言分析.md     # 问题文件3 (包含Q1-Q6)
├── ...                         # 其他28个问题文件
└── 问答重建系统Prompt.md         # 本执行指南
```

### 目标输出结构
```
工作目录/纯问题集重建/
├── [原有问题文件...]
└── 重建回答/                    # 新建文件夹
    ├── README.md                # 回答集说明文档
    ├── 进度报告.md              # 处理进度跟踪
    ├── 11维度_回答.md           # 对应回答文件1
    ├── Apple外区id指南_回答.md   # 对应回答文件2
    ├── telegram创始人发言分析_回答.md # 对应回答文件3
    └── ...                     # 其他对应回答文件
```

## 问题文件格式解析

每个问题文件包含：
- **文件标题**: `# [主题名] - 纯问题集（重建版）`
- **问题编号**: `## Q1`, `## Q2`, `## Q3`...
- **问题内容**: 每个Q编号下的完整问题描述

### 问题类型识别
1. **YouTube视频转写请求**: 包含视频链接和转写要求
2. **对话历史整理请求**: 要求整理为Markdown格式
3. **深度分析请求**: 复杂的技术、哲学、社会问题
4. **内容继续请求**: 简单的"继续"指令
5. **专业咨询请求**: 特定领域的专业问题

## 执行流程

### 第一步：环境准备
1. 创建 `工作目录/纯问题集重建/重建回答/` 文件夹
2. 创建进度跟踪文件
3. 初始化任务管理系统

### 第二步：文件处理循环
对每个问题文件执行以下步骤：

#### 2.1 任务创建
```
使用add_tasks工具为每个问题文件创建独立任务：
- 任务名称: "处理[文件名]问答重建"
- 任务描述: "为[文件名]中的Q1-Qn生成对应的A1-An回答"
```

#### 2.2 问题解析
1. 读取问题文件内容
2. 识别所有Q编号及其对应问题
3. 分析问题类型和上下文关系
4. 确定回答策略

#### 2.3 回答生成
为每个问题生成高质量回答，遵循以下原则：

**上下文连贯性**:
- Q1的回答为后续问题建立基础
- Q2-Qn的回答应考虑前面问题的上下文
- 保持整个文件内的对话连贯性

**回答质量标准**:
- 准确性：基于真实信息和专业知识
- 完整性：充分回答问题的所有方面
- 实用性：提供可操作的建议和信息
- 专业性：使用适当的专业术语和深度

#### 2.4 格式规范
生成的回答文件格式：

```markdown
# [主题名] - 重建回答

## 问答对话重建

### Q1
[原始问题内容]

### A1
[生成的回答内容]

### Q2
[原始问题内容]

### A2
[生成的回答内容，考虑Q1-A1的上下文]

### Q3
[原始问题内容]

### A3
[生成的回答内容，考虑前面所有Q&A的上下文]

...

## 文件处理信息
- **原始问题数**: [数量]
- **生成回答数**: [数量]
- **主要问题类型**: [类型列表]
- **处理时间**: [时间戳]
- **上下文连贯性**: ✅ 已保持
```

### 第三步：进度管理

#### 3.1 任务状态更新
- 开始处理时：将任务状态更新为IN_PROGRESS
- 完成处理时：将任务状态更新为COMPLETE
- 遇到问题时：记录问题并标记为需要关注

#### 3.2 进度报告维护
在 `重建回答/进度报告.md` 中记录：
- 已处理文件列表
- 每个文件的问题数量和回答数量
- 处理时间和质量评估
- 遇到的问题和解决方案

## 特殊处理规则

### YouTube视频转写请求
- 提供专业的视频转写方法和工具推荐
- 解释技术实现细节和最佳实践
- 考虑用户的渐进式需求演进

### 对话历史整理请求
- 提供标准的Markdown格式化方法
- 解释格式规范的重要性
- 提供自动化工具建议

### 深度分析请求
- 提供多角度的分析视角
- 引用相关理论和实例
- 保持客观和平衡的观点

### 内容继续请求
- 基于前面的上下文继续展开
- 保持内容的连贯性和深度
- 适当总结和过渡

## 质量控制

### 回答质量检查清单
- [ ] 回答是否准确和专业
- [ ] 是否考虑了上下文关系
- [ ] 格式是否符合规范
- [ ] 是否保持了对话的连贯性
- [ ] 是否提供了实用价值

### 文件完整性检查
- [ ] 所有Q编号都有对应的A编号
- [ ] 文件格式正确
- [ ] 处理信息完整
- [ ] 进度报告已更新

## 执行命令模板

```
# 开始执行
1. 创建重建回答文件夹
2. 初始化进度跟踪
3. 为每个问题文件创建任务
4. 按顺序处理每个文件
5. 生成高质量回答
6. 更新进度报告
7. 完成质量检查
```

## 预期成果

完成后将获得：
- 30个高质量的问答对话文件
- 220+个专业回答
- 完整的进度跟踪记录
- 可用于训练和研究的问答数据集

## 技术实现细节

### 文件命名规则
- 回答文件名: `[原问题文件名]_回答.md`
- 保持与原文件的一一对应关系
- 使用UTF-8编码确保中文字符正确显示

### 上下文处理策略

#### 单文件内上下文
```
Q1 → A1 (建立基础上下文)
Q2 → A2 (基于Q1-A1的上下文)
Q3 → A3 (基于Q1-A1, Q2-A2的累积上下文)
...
```

#### 跨文件上下文
- 相关主题文件间的交叉引用
- 技术概念的一致性维护
- 用户需求演进的连贯性

### 回答生成策略

#### 针对不同问题类型的回答策略

**1. YouTube视频转写类**
```
回答要素:
- 技术实现方案 (Whisper, API调用等)
- 工具推荐和使用方法
- 质量控制和验证步骤
- 格式化和后处理技巧
- 常见问题和解决方案
```

**2. 深度分析类**
```
回答要素:
- 多维度分析框架
- 理论基础和实例支撑
- 数据和事实引用
- 不同观点的平衡呈现
- 实际应用和启示
```

**3. 技术咨询类**
```
回答要素:
- 具体操作步骤
- 工具和资源推荐
- 注意事项和风险提示
- 替代方案和优化建议
- 相关扩展知识
```

### 质量评估标准

#### 回答质量评分 (1-5分)
- **准确性** (5分): 信息准确，无错误
- **完整性** (5分): 全面回答问题各个方面
- **实用性** (5分): 提供可操作的价值
- **专业性** (5分): 展现专业知识深度
- **连贯性** (5分): 与上下文保持一致

#### 最低质量要求
- 每个回答至少200字
- 必须直接回答问题核心
- 提供具体可行的建议
- 保持专业和客观的语调

## 错误处理和异常情况

### 常见问题处理
1. **问题内容不清晰**: 基于上下文推断意图，提供最可能的解释
2. **技术问题过时**: 提供当前最佳实践，并说明技术演进
3. **主观性问题**: 提供多角度观点，保持平衡
4. **重复性问题**: 在回答中建立与前面内容的联系

### 文件处理异常
- 文件读取失败: 记录错误并跳过，在进度报告中标记
- 格式解析错误: 尝试手动解析，记录问题
- 编码问题: 使用UTF-8重新处理

## 最终验收标准

### 完成度检查
- [ ] 30个问题文件全部处理完成
- [ ] 每个Q都有对应的A
- [ ] 所有回答文件格式正确
- [ ] 进度报告完整准确

### 质量检查
- [ ] 随机抽查10个文件的回答质量
- [ ] 验证上下文连贯性
- [ ] 检查专业术语使用准确性
- [ ] 确认实用价值和可操作性

---

**执行提示**: 请严格按照此prompt执行，确保每个步骤都得到正确实施，保持高质量的输出标准。建议分批处理文件，每处理5个文件后进行一次质量检查和进度更新。
