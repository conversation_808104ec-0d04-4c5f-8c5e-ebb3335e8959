# 新会话启动指令

## 🚀 立即开始执行

请在新会话中直接执行以下指令：

```
继续处理大规模MD文件问答重建项目。

当前状态：
- 已完成48个重要文件，270+个问题
- 完成率约60%+
- 需要继续处理剩余20-30个重要文件

请按照以下步骤继续：

1. 查看项目根目录，识别未处理的文件
2. 优先处理问题数量多的重要文件
3. 为每个文件生成高质量的问答对话
4. 保持已建立的质量标准和格式规范
5. 及时更新任务状态

目标：向70%+完成率迈进，最终建立完整的问答数据集。

请开始执行。
```

## 📋 执行检查清单

### 开始前检查
- [ ] 查看 `instruct/快速执行指南.md` 了解详细流程
- [ ] 查看 `temp/CB/纯问题集重建/重建回答/当前进度状态.md` 了解当前进度
- [ ] 使用 `view_tasklist` 查看任务管理状态

### 执行过程
- [ ] 使用 `view .` 查看根目录文件列表
- [ ] 识别未处理的重要文件
- [ ] 优先处理问题数量多的文件（5+问题）
- [ ] 为每个文件创建处理任务
- [ ] 生成高质量回答文件
- [ ] 更新任务状态为完成

### 质量标准
- [ ] 每个问题都有对应的详细回答
- [ ] 回答具有深度分析和专业性
- [ ] 保持格式规范和结构完整
- [ ] 提供实用价值和指导意义

## 🎯 重要提醒

1. **优先级**：问题数量多的文件 > 内容重要的文件 > 单问题文件
2. **质量**：保持已建立的高质量标准，不要为了速度牺牲质量
3. **进度**：定期更新任务状态，及时总结进度成果
4. **效率**：对于超大文件（如33问题），可以分批处理核心部分

## 📁 关键文件路径

- **指导文件**：`instruct/快速执行指南.md`
- **进度状态**：`temp/CB/纯问题集重建/重建回答/当前进度状态.md`
- **回答文件夹**：`temp/CB/纯问题集重建/重建回答/`
- **原始文件**：项目根目录中的各个.md文件

---

**准备就绪，开始新会话执行！**
