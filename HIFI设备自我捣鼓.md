# HIFI设备自我捣鼓 - 纯问题集

## Q1
请你对我下面的个人笔记进行逻辑分析，详细地解释其中的逻辑，我的应对处理正确与否：

Hi- Fi 播放折腾

手头有的谈得上摸得到Hi- Fi的设备就有两台，

水月雨兰LAN入耳式耳机 ¥179，配一个31993小尾巴（用来转type-c连手机用的）

和早期的二代宝华韦健PX7头戴式耳机，

这两台设备买回来大部分时间都用默认模式（或蓝牙），因为那个音质提升已经足够震撼了。

这段时间又想寻求折腾提升音质体验，所以经常搜索更新过的px7S2、px7S2E、哈曼卡顿水晶4（怕扰民没买）。

昨夜心血来潮，疯狂测试，入耳式hifi，有无小尾巴解码器下的声音状态，px7在3.5mm直接连接，蓝牙模式下音质表现。因为个人感觉3.5mm直推的声音（无论入耳式还是头戴式）总是感觉很平淡均衡，仿佛各个频段的音量是一致的，没有之前折腾的snoyxm3那种明显的可以设置各频段突出的感觉。遂上网查攻略

初步了解，3.5mm相当于传输模拟信号，用的是Windows（或其他笔记本内置声卡），基本上听个响，怪不得感觉声音特别均衡。因为这就是win平台扬声器的播放特点，配器音量和人声一致，比较注重还原真实感，但是失去了愉悦的收听感受。打个不恰当的比方，就像是在音乐会现场，但是不是在听众席上的位置收听，而是透过一个敞开的门在隔壁房间收录的，感觉就是该有的都有，比较还原，但是人声不够突出，往往配器和环境音是主要的，很符合在远处录音的特点。尽管空间关系和3d效果都有，但是就是混为一谈。总体感觉，就是平淡沉闷，有些过于低频的配器声音因为Windows声卡解析不了，反而会在频率上偏高（能解码的下限），混在一起，有所失真，十分难受。

px7s2蓝牙直连mac一直是我过去以为的音质标杆。一方面，mac平台的蓝牙体验要比win平台好太多，不会出现莫名其妙的延时，卡顿爆破音，apple music的绝大多数无损也很不错。绝大部分apple music能提供的音质都是16bit 44.1khz起步。但是网上说蓝牙会有失真，但是总归是数字信号，解码任务转给px7内置的quantlum解码（跳过windows声卡）应该更好实现宝华韦健出厂对耳机动圈的调度优化，这就是更优秀的听感来源原因。

后翻找购买的京东链接，发现px7链接页面清清楚楚写着，最高有usb-A转type-c可以实现最高24bit，96khz的音质。恍然大悟，原先蓝牙最高只能传输16bit，48khz左右的数据，现在换为usb线直连，可以实现最高的采样率和位深，同时跳过了windows声卡，由耳机内置声卡解码，原厂调度，一定音质最佳。上手实测，仿佛天籁之音，从未有过的丝滑，人声能够与配器恰到好处的分离，丝毫不粘连，既没有了3.5mm直连的时候低频过于沉闷压住人声的问题，也没有蓝牙模式下人声频率和配器比较接近黏糊在一起的感觉。同时，流媒体播放平台因为有线传输，似乎切换了更高音质的版本，能够听到前所未有的细节，可以听见歌手的每一处吸气声，相得益彰，身临其境，天籁之声，爽滑满天。

这种播放模式下有哪些优点呢，一方面流媒体平台能够自动解锁高音质（有线+解码器触发），而且使用原厂调度避免了3.5mm均衡调度在大动圈耳机上的过于平淡，中频人声清晰稳定，高频人声略带亮色（因为有电力驱动，规避了3.5mm电脑推动功率不足声音小的问题），配器方面，中低频还原准确，恢复迅速没有粘连，Dua Lipa 的 New Rules里面的复杂鼓点清晰、准确，恢复迅速。所谓配器和人声分离清晰，是指和蓝牙模式下对比，蓝牙下感觉人声和配器在左右方向上是单一的，也就是左声道就是左声道，右声道就是右声道，频率稍微有点贴近容易混淆。而现在左声道内部能清晰听到丰富的层次感，能听到人声在左面一定距离，配器声音仿佛来自于左前方/或者左后方，有额外的空间关系，或者额外的层次感（更远的距离上），这种丰富的听感直接吊打蓝牙模式。

想要冲击更高的音质，尝试用小尾巴的解码器实现384khz 32bit的推流，发现系统可以识别输出，但是更大的问题是流媒体平台一般无法提供这么高位深和采样率的原始音频，使用入耳式耳机，原生3.5mm直连电脑，大约能实现24bit，96khz的输出，但是会有杂音，电流声，解析不准确，高频，低频都有所失真，使用解码器转接，实现了我在入耳式耳机上听过的最高音质，几乎和上面说的头戴式耳机一样出色的解码表现，还原准确，高频，低频在小尾巴的解码下虽然各频段柔和之余更为均衡，但是音色、频率都无比还原真实，远比3.5mm入耳直连Windows声卡解析要精确的多。但是尽管如此，还是和宝华韦健有差距，因为大动圈能提供更加丰富的空间层次感，这是入耳式小动圈所无法实现的，但是就人声、配器的音色频率来说已经几乎是一样清晰不失真，唯一的差距就在于人声和配器的分离，配器的空间层次感不足头戴式耳机。

此时想要冲击更高音质，用小尾巴解码器，3.5mm转连接头戴耳机，的确能实现上述的采样率和位深，但是解码器默认的解码设置应该是为了入耳式耳机或者音响设置的，所以解码出来的音质更为均衡柔和，而且也遭遇了3.5mm功率不足的问题，整体来说还原准确，和上述的入耳式耳机接小尾巴应该是一样的模拟信号，但是问题就在于没有使用头戴耳机内置解码器会错失原厂调教的对于大动圈的空间效果的呈现，听感上果然失去了丰富的层次感和空间关系，所以这个小尾巴解码器的确是有实力的，用来拯救入耳式耳机或者音响音质都是一绝，但是头戴式耳机往往还是要利用原厂的内置声卡的解码调度，才能够释放全部的丰富听觉层次。

总结：

1. 入耳式耳机：小尾巴解码器 > 3.5mm直连电脑
2. 头戴式耳机：USB直连（使用内置解码器）> 蓝牙 > 小尾巴解码器 > 3.5mm直连电脑

### Q2

这样的结论是否正确？我的测试方法和理解有什么问题吗？
