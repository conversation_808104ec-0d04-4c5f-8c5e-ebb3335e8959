# 游戏学习效率与策略差异 - 纯问题集

## Q1
为什么同样的实践经历（以玩游戏为例）忽视之前的知识背景和主观意愿，有的人能从中快速学习，通过反馈及时调整，实现游戏能力的快速提高，而有的人却要慢很多？比方说同样200小时的游戏时长，有的人可能已经把所有可能遇见的情形都遍历过了，成为了基于经验的游戏高手，即便手上操作能力不足，但是经验丰富程度已经足够正确预判分析处理绝大多数情景——但是有的人学习的很慢，他们因为不主动探索和实践新的玩法，所以导致经验空间无法拓展，基础操作无法巩固，心态上面临未知情形也更不稳定。究竟怎样才算是有效的学习策略？应该结合前人的经验和自己的关注点多多探索错误情形，在所有的错误失败经验中建立起自己对于正确的理解？

### Q2

我觉得这是一个非常现实的问题，很多学习成本大或者启动成本高的过程，比如自学一门技能，或者是dota2，彩虹六号，尽管前期会在大量的负反馈中被折磨，但是一旦建立起和游戏环境有效互动的正反馈循环，玩家的主动性就会被调动起来，此时大量学习成本的沉没反而会把人停留在舒适区，成为游戏里不愿离去的老玩家。现实中的一些复杂技能也是一样，比如学一门语言，或者编程，只要跨过了前期巨大投入的门槛，就会进入一种无法舍弃的习得技能的舒适区
