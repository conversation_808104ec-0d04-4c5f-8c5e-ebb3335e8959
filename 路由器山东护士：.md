# 路由器山东护士： - 纯问题集

## Q1
请为我全面理解并概括综述这段文字中想要表达什么。此外，详细解释下面这段文字中你认为比较晦涩且不易于直观理解的，各个具体的互联网专业术语和缩略语的具体指代和详细含义，并做展开介绍

之前用的小米ax6000，每天几千次访问这个域名。更换路由器后，无类似问题。恩山论坛有大神解包过，有兴趣可以自己去看看，反正我是不相信这东西干的都是好事。光猫桥接，后面全部使用国外或是开源的东西，routeros，opwrt，adg，pihole，pfsence等等，nas用的unraid。dns自建，上游全部dot/doh，内外dns隔离并且流量也分流。今年清理了全部国产智能设备，备用机从小米换成三星，音箱换了HomePod，电视三星，电视盒子也换成Appletv，目前除了光猫无解之外已经基本没有任何国产设备。手机平板里国产app尤其字节跳动系列一律禁用了读取应用列表的权限，输入法从搜狗换成苹果和三星的自带。pc一些看着不那么顺眼不过又必须用的软件比如qq微信网盘等等，一律扔进虚拟机。苹果设备全部美区id杜绝云上贵州，三星设备应用商店等全部更换海外版本。目前极少出现聊天提到的内容被淘宝京东推送的情况了，并且广告明显减少。顺便猜猜作为主业护士副业教钢琴的女性，在从零开始捣鼓这些东西的时候花了多少时间和脑细胞。

## Q2
感谢评论指教，猫棒代替光猫目前症状良好。顺便说下私信最多的dns分流，作用是访问国内时候就用国内的dns延迟低一些，访问国外就用国外的防止污染。当然上游分别都是自建的，如果直接填公共dns那屁用没有，具体方法自行谷歌。至于分流实现的方法很多，比如mosdns，smartdns等等。我是买了routeros的pu权限总不能浪费，就用了static列表来实现的分流功能，实际跟mos没什么区别domain列表一毛一样的。然后是完全去除了opwrt，不稳定且编译未必可靠。闲鱼摸了一台m2芯片选配10g网口的macmini，surge网关模式接管dhcp顺便那个，还多了个小电脑用，挺好。加一句surge这鬼东西ios和mac是特么分开订阅，割的一手好韭菜。其他一些原本依靠opwrt的功能比如ddns之类统统送去nas上的docker了，还扔进去一个可来屎用作万一那个挂了的备用，顺便解决surge之后unraid市场ssl的问题。现在还存在的问题是nas网关如果指向主路由的话emby等等需要刮削的东西就被墙，如果指向那个，拐带着qb等等下载工具也去了那个。。默默继续研究unraid的vlan或是其他的分流方式中。。
